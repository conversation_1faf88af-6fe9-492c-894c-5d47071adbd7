const parser1 = {
  parse: () => 1
}

const parser2 = () => '123'

const parse3 = {
  extract: () => true
}

// bad
type GetParserResult1<T> = T extends { parse: () => infer TResult }
  ? TResult
  : T extends { extract: () => infer TResult }
  ? TResult
  : T extends () => infer TResult
  ? TResult
  : never;

// good
type GetParserResult<T> = T extends
  | {
      parse: () => infer TResult;
    }
  | {
      extract: () => infer TResult;
    }
  | (() => infer TResult)
  ? TResult
  : never;

type Parser1 = GetParserResult<typeof parser1>
type Parser2 = GetParserResult<typeof parser2>
type Parser3 = GetParserResult<typeof parse3>