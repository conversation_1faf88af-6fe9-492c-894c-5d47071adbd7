const programModeEnumMap = {
  GROUP: 'group',
  ANNOUNCEMENT: 'announcement',
  ACTIVITY: 'activity',
  EXAM: 'exam',
  HOMEWORK: 'homework',
  MATERIAL: 'material',
  QUESTION: 'question',
  COMMENT: 'comment',
  LIKE: 'like',
  REPL<PERSON>: 'reply',
  F<PERSON><PERSON><PERSON>: 'follow',
  UNFOLLOW: 'unfollow',
  JOIN: 'join',
  LEAVE: 'leave',
  INVITE: 'invite',
} as const;

export type IndividualProgramMode = typeof programModeEnumMap[
  | 'GROUP'
  | 'EXAM'
  | 'LIKE'
];
