const TypePurchases = {
  firstSelfHostLicensePurchase: "first_purchase",
  renewalSelfHost: "renewal_self",
  monthlySubscription: "monthly_subscription",
  annualSubscription: "annual_subscription",
} as const;

type Valueof<T> = T[keyof T];

type MetaDataGatherWireTransferKeys = `${Valueof<typeof TypePurchases>}_alt_payment_method`
type CustomerMetaDataGatherWireTransfer = Partial<Record<MetaDataGatherWireTransferKeys, string>>