const programModeEnumMap = {
  GROUP: 'group',
  ANNOUNCEMENT: 'announcement',
  ACTIVITY: 'activity',
  EXAM: 'exam',
  HOMEWORK: 'homework',
  MATERIAL: 'material',
  QUESTION: 'question',
  COMMENT: 'comment',
  LIKE: 'like',
  <PERSON><PERSON><PERSON><PERSON>: 'reply',
  F<PERSON><PERSON><PERSON>: 'follow',
  UNFOL<PERSON>OW: 'unfollow',
  JOIN: 'join',
  LEAVE: 'leave',
  INVITE: 'invite',
} as const;

export type Group = typeof programModeEnumMap.GROUP;
export type Announcement = typeof programModeEnumMap.ANNOUNCEMENT;
export type Activity = typeof programModeEnumMap.ACTIVITY;
export type Exam = typeof programModeEnumMap.EXAM;
export type Homework = typeof programModeEnumMap.HOMEWORK;
export type Material = typeof programModeEnumMap.MATERIAL;
export type Question = typeof programModeEnumMap.QUESTION;
export type Comment = typeof programModeEnumMap.COMMENT;
